<script setup>
import {
  onMounted,
  ref,
} from 'vue'

import router from '@/router'
import useSocialMedia from '@/stores/socialMedia'

const socialMediaDetails = ref(null);
onMounted(async () => {
    socialMediaDetails.value = await useSocialMedia().getSocialMediaDetails();

    // socialMediaDetails.value = useSocialMedia().socialMediaDetails;
    console.log(socialMediaDetails.value.email);
})  
</script>
<template>
    <header class="vs-header header-layout1">
        <div class="header-top">
            <div class="container">
                <div class="row justify-content-between align-items-center">
                    <div class="col-auto d-none d-lg-block">
                        <div class="header-links style-white">
                            <ul>
                                <li><router-link to="/admission"><i class="far fa-user-circle"></i>Login &
                                        Register</router-link></li>
                                <li><a href="contact.html" class="searchBoxTggler"><i class="far fa-search"></i>Search
                                        Keyword</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-lg-auto text-center">
                        <div class="header-links style2 style-white">
                            <ul>
                                <li><i class="fas fa-envelope"></i>Email: <a href="mailto:<EMAIL>">{{
                                    socialMediaDetails?.email }}</a></li>
                                <li><i class="fas fa-mobile-alt"></i>Phone: <a href="tel:+4402076897888">{{
                                    socialMediaDetails?.contact_number }}</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="sticky-wrap">
            <div class="sticky-active">
                <div class="container">
                    <div class="row gx-3 align-items-center justify-content-between">
                        <div class="col-8 col-sm-auto">
                            <div class="header-logo">
                                <router-link to="/">
                                    <img width="150" src="/img/original.png" alt="logo">
                                    <!-- <img  src="/frontend/assets1/img/logo.svg" alt="logo"> -->

                                </router-link>
                            </div>
                        </div>
                        <div class="col text-end text-lg-center">
                            <nav class="main-menu menu-style1 d-none d-lg-block">
                                <ul>

                                    <li>
                                        <router-link to="/about">About Us</router-link>
                                    </li>
                                    <li class="menu-item-has-children">
                                        <a href="match.html">Classes</a>
                                        <ul class="sub-menu">
                                            <li><a href="class.html">Class Style 1</a></li>
                                            <li><a href="class-2.html">Class Style 2</a></li>
                                            <li><a href="class-details.html">Class Details</a></li>
                                        </ul>
                                    </li>
                                    <li class="menu-item-has-children">
                                        <a href="blog.html">Blog</a>
                                        <ul class="sub-menu">
                                            <li><a href="blog.html">Blog</a></li>
                                            <li><a href="blog-details.html">Blog Details</a></li>
                                        </ul>
                                    </li>
                                    <li class="menu-item-has-children mega-menu-wrap">
                                        <a href="#">Pages</a>
                                        <ul class="mega-menu">
                                            <li><a href="shop.html">Pagelist 1</a>
                                                <ul>
                                                    <li><a href="index.html">Demo Style 1</a></li>
                                                    <li><a href="index-2.html">Demo Style 2</a></li>
                                                    <li><a href="index-3.html">Demo Style 3</a></li>
                                                    <li><a href="class.html">Class Style 1</a></li>
                                                    <li><a href="class-2.html">Class Style 2</a></li>
                                                    <li><a href="class-details.html">Class Details</a></li>
                                                </ul>
                                            </li>
                                            <li><a href="#">Pagelist 2</a>
                                                <ul>
                                                    <li><a href="about.html">About Us</a></li>
                                                    <li><a href="service.html">Service</a></li>
                                                    <li><a href="service-details.html">Service Details</a></li>
                                                    <li><a href="team.html">Team</a></li>
                                                    <li><a href="team-details.html">Team Details</a></li>
                                                    <li><a href="gallery.html">Gallery</a></li>
                                                </ul>
                                            </li>
                                            <li><a href="#">Pagelist 3</a>
                                                <ul>
                                                    <li><a href="event-details.html">Event Details</a></li>
                                                    <li><a href="shop.html">Shop</a></li>
                                                    <li><a href="shop-details.html">Shop Details</a></li>
                                                    <li><a href="cart.html">Shopping Cart</a></li>
                                                    <li><a href="checkout.html">Checkout</a></li>
                                                    <li><a href="price-plan.html">Price Plan</a></li>
                                                </ul>
                                            </li>
                                            <li><a href="#">Pagelist 4</a>
                                                <ul>
                                                    <li><a href="faq.html">FAQ</a></li>
                                                    <li><a href="blog.html">Blog</a></li>
                                                    <li><a href="blog-details.html">Blog Details</a></li>
                                                    <li><a href="registration.html">Registration</a></li>
                                                    <li><a href="contact.html">Contact</a></li>
                                                    <li><a href="error.html">Error Page</a></li>
                                                </ul>
                                            </li>
                                        </ul>
                                    </li>
                                    <li>
                                        <a href="contact.html">Contact</a>
                                    </li>
                                </ul>
                            </nav>
                            <button class="vs-menu-toggle d-inline-block d-lg-none"><i class="fal fa-bars"></i></button>
                        </div>
                        <div class="col-auto  d-none d-lg-block">
                            <div class="header-icons">
                                <button class="simple-icon sideMenuToggler"><i class="far fa-bars"></i></button>
                            </div>
                        </div>
                        <!-- <div class="col-auto d-none d-xl-block">
                            <button href="#" class="vs-btn sideMenuToggler">Apply Today</button>
                        </div> -->
                        <div class="col-auto d-none d-xl-block">
                            <button href="#" class="vs-btn sideMenuToggler">Apply Today</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
</template>