{"name": "child-2", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@primeuix/themes": "^1.1.1", "@tailwindcss/vite": "^4.1.7", "axios": "^1.8.4", "gsap": "^3.12.7", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "pinia": "^3.0.1", "primevue": "^4.3.4", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue-slick-carousel": "^1.0.6", "vue3-toastify": "^0.2.8"}, "devDependencies": {"@eslint/js": "^9.21.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.2.0", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-plugin-vue": "~10.0.0", "globals": "^16.0.0", "postcss": "^8.5.3", "prettier": "3.5.3", "tailwindcss": "^4.1.7", "vite": "^6.2.1", "vite-plugin-vue-devtools": "^7.7.2"}}