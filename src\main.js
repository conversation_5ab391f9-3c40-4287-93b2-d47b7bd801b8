// import './assets/main.css'
//backend main css file...
// import '../public/style.css'
// Frontend css file...
// import '../public/frontend/assets/css/main.css'

// import '../public/frontend/assets1/css/style.css'

import { createApp } from 'vue'

import { createPinia } from 'pinia'
import PrimeVue from 'primevue/config'

import App from './App.vue'
import router from './router'

const app = createApp(App)

app.use(createPinia())
app.use(router)
// app.use(VueDataTable)
app.use(PrimeVue);

app.mount('#app')
